// Simple script to apply guild chat migration
import { createClient } from '@supabase/supabase-js';
import fs from 'fs';

// Use the correct Supabase URL and service key
const supabaseUrl = 'https://ajanudmpznxgmfzxzjwo.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqYW51ZG1wem54Z21menhaamdvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNTIwNzE0NSwiZXhwIjoyMDUwNzgzMTQ1fQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createGuildMessagesTable() {
  try {
    console.log('Creating guild_messages table...');
    
    // Read the SQL file
    const sqlContent = fs.readFileSync('create_guild_messages_table.sql', 'utf8');
    
    // Split into individual statements
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`Executing ${statements.length} SQL statements...`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`Executing statement ${i + 1}: ${statement.substring(0, 50)}...`);
      
      const { data, error } = await supabase
        .from('_sql')
        .select('*')
        .limit(1);
      
      if (error) {
        console.log('Trying direct SQL execution...');
        // Try direct SQL execution
        const { error: sqlError } = await supabase.rpc('exec_sql', { 
          sql: statement 
        });
        
        if (sqlError) {
          console.error(`Error executing statement ${i + 1}:`, sqlError);
          // Continue with next statement instead of failing completely
          continue;
        }
      }
      
      console.log(`✅ Statement ${i + 1} executed successfully`);
    }
    
    console.log('✅ Guild messages table creation completed!');
    
    // Test the table by trying to query it
    const { data, error } = await supabase
      .from('guild_messages')
      .select('*')
      .limit(1);
    
    if (error) {
      console.log('⚠️  Table created but query test failed:', error.message);
    } else {
      console.log('✅ Table query test successful!');
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  }
}

// Run the migration
createGuildMessagesTable();
