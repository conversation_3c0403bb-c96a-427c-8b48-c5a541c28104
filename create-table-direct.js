// Direct approach to create guild_messages table
// Using built-in fetch (Node.js 18+)

const SUPABASE_URL = 'https://ajanudmpznxgmfzxzjwo.supabase.co';
const SUPABASE_SERVICE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFqYW51ZG1wem54Z21menhaamdvIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNTIwNzE0NSwiZXhwIjoyMDUwNzgzMTQ1fQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

async function createTable() {
  try {
    console.log('Attempting to create guild_messages table...');
    
    const sql = `
      CREATE TABLE IF NOT EXISTS public.guild_messages (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        guild_id UUID NOT NULL,
        user_id UUID NOT NULL,
        content TEXT NOT NULL,
        message_type TEXT DEFAULT 'user',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        is_edited BOOLEAN DEFAULT FALSE
      );
    `;
    
    const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/exec_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${SUPABASE_SERVICE_KEY}`,
        'apikey': SUPABASE_SERVICE_KEY
      },
      body: JSON.stringify({ sql })
    });
    
    if (response.ok) {
      console.log('✅ Table created successfully!');
    } else {
      const error = await response.text();
      console.error('❌ Failed to create table:', error);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

createTable();
